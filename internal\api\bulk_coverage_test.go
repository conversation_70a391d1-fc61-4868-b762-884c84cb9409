package api

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	_ "modernc.org/sqlite"
)

// Bulk test file to rapidly increase coverage by testing multiple handlers at once
// Focus on the biggest coverage gaps first

func init() {
	gin.SetMode(gin.TestMode)
}

// TestBulkHandlerCoverage tests multiple handlers to rapidly increase coverage
func TestBulkHandlerCoverage(t *testing.T) {
	// Test SafeHandler wrapper
	t.Run("SafeHandler_Normal", func(t *testing.T) {
		handler := SafeHandler(func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})

		router := gin.New()
		router.GET("/test", handler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)
	})

	// Test error response functions
	t.Run("ErrorResponse_Functions", func(t *testing.T) {
		// Test NewAppError
		err := NewAppError(ErrValidation, "test error")
		assert.Equal(t, ErrValidation, err.Code)
		assert.Equal(t, "test error", err.Message)

		// Test RespondError
		router := gin.New()
		router.GET("/error", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrValidation, "validation failed"))
		})

		req := httptest.NewRequest("GET", "/error", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 400, w.Code)

		var response map[string]interface{}
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, jsonErr)
		assert.False(t, response["success"].(bool))
	})

	// Test RespondSuccess
	t.Run("RespondSuccess_Function", func(t *testing.T) {
		router := gin.New()
		router.GET("/success", func(c *gin.Context) {
			RespondSuccess(c, map[string]string{"message": "success"})
		})

		req := httptest.NewRequest("GET", "/success", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, jsonErr)
		assert.True(t, response["success"].(bool))
	})

	// Test getValidArticleID helper
	t.Run("getValidArticleID_Helper", func(t *testing.T) {
		router := gin.New()
		router.GET("/article/:id", func(c *gin.Context) {
			id, ok := getValidArticleID(c)
			if !ok {
				return // Error already sent
			}
			c.JSON(200, gin.H{"id": id})
		})

		// Test valid ID
		req := httptest.NewRequest("GET", "/article/123", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, 200, w.Code)

		// Test invalid ID
		req = httptest.NewRequest("GET", "/article/invalid", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, 400, w.Code)
	})
}

// TestDatabaseOperations tests database-related functions
func TestDatabaseOperations(t *testing.T) {
	// Create in-memory test database
	db, err := sqlx.Open("sqlite", ":memory:")
	assert.NoError(t, err)
	defer db.Close()

	// Create test schema
	schema := `
	CREATE TABLE articles (
		id INTEGER PRIMARY KEY,
		title TEXT,
		content TEXT,
		source TEXT,
		url TEXT UNIQUE,
		pub_date TIMESTAMP,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		status TEXT DEFAULT 'pending',
		composite_score REAL,
		confidence REAL
	);
	CREATE TABLE llm_scores (
		id INTEGER PRIMARY KEY,
		article_id INTEGER,
		model TEXT,
		score REAL,
		metadata TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);
	CREATE TABLE feedback (
		id INTEGER PRIMARY KEY,
		article_id INTEGER,
		user_id TEXT,
		category TEXT,
		comment TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);
	CREATE TABLE manual_scores (
		id INTEGER PRIMARY KEY,
		article_id INTEGER,
		score REAL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);`

	_, err = db.Exec(schema)
	assert.NoError(t, err)

	// Insert test data
	_, err = db.Exec(`
		INSERT INTO articles (id, title, content, source, url, pub_date, status, composite_score, confidence)
		VALUES (1, 'Test Article', 'Test content', 'Test Source', 'http://test.com', ?, 'analyzed', 0.5, 0.8)
	`, time.Now())
	assert.NoError(t, err)

	// Test createArticleHandler with database
	t.Run("createArticleHandler_Database", func(t *testing.T) {
		handler := createArticleHandler(db)
		router := gin.New()
		router.POST("/articles", handler)

		requestBody := map[string]interface{}{
			"title":    "New Article",
			"content":  "New content",
			"source":   "New Source",
			"url":      "http://new.com",
			"pub_date": time.Now().Format(time.RFC3339),
		}

		body, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/articles", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)
		assert.Equal(t, 201, w.Code)
	})

	// Test getArticlesHandler with database
	t.Run("getArticlesHandler_Database", func(t *testing.T) {
		handler := getArticlesHandler(db)
		router := gin.New()
		router.GET("/articles", handler)

		req := httptest.NewRequest("GET", "/articles", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)
		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, jsonErr)
		assert.True(t, response["success"].(bool))
	})

	// Test getArticleByIDHandler with database
	t.Run("getArticleByIDHandler_Database", func(t *testing.T) {
		handler := getArticleByIDHandler(db)
		router := gin.New()
		router.GET("/articles/:id", handler)

		req := httptest.NewRequest("GET", "/articles/1", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)
		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, jsonErr)
		assert.True(t, response["success"].(bool))
	})

	// Test biasHandler with database
	t.Run("biasHandler_Database", func(t *testing.T) {
		// Insert LLM scores
		_, err = db.Exec(`
			INSERT INTO llm_scores (article_id, model, score, metadata)
			VALUES (1, 'gpt-4', 0.6, '{"confidence": 0.8}')
		`)
		assert.NoError(t, err)

		handler := biasHandler(db)
		router := gin.New()
		router.GET("/articles/:id/bias", handler)

		req := httptest.NewRequest("GET", "/articles/1/bias", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)
		assert.Equal(t, 200, w.Code)
	})

	// Test manualScoreHandler with database
	t.Run("manualScoreHandler_Database", func(t *testing.T) {
		handler := manualScoreHandler(db)
		router := gin.New()
		router.POST("/manual-score/:id", handler)

		requestBody := map[string]interface{}{
			"score": 0.7,
		}

		body, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/manual-score/1", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)
		assert.Equal(t, 200, w.Code)
	})
}

// TestUtilityFunctions tests various utility functions
func TestUtilityFunctions(t *testing.T) {
	// Test safeLogf function
	t.Run("safeLogf_Function", func(t *testing.T) {
		// This should not panic
		safeLogf("Test message: %s", "test")
		safeLogf("Test message with no args")
	})

	// Test error constants exist
	t.Run("ErrorConstants_Exist", func(t *testing.T) {
		// Test that error constants are defined and can be used
		err1 := NewAppError(ErrValidation, "validation error")
		err2 := NewAppError(ErrNotFound, "not found error")
		err3 := NewAppError(ErrConflict, "conflict error")
		err4 := NewAppError(ErrInternal, "internal error")

		assert.Equal(t, "validation error", err1.Message)
		assert.Equal(t, "not found error", err2.Message)
		assert.Equal(t, "conflict error", err3.Message)
		assert.Equal(t, "internal error", err4.Message)
	})
}
