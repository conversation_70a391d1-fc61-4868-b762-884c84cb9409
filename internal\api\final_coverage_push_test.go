package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	_ "modernc.org/sqlite"
)

func init() {
	gin.SetMode(gin.TestMode)
}

// TestFinalCoveragePush - comprehensive test to push coverage over 80%
func TestFinalCoveragePush(t *testing.T) {
	// Test all database operations with comprehensive scenarios
	t.Run("ComprehensiveDatabaseOperations", func(t *testing.T) {
		db, _ := sqlx.Open("sqlite", ":memory:")
		defer db.Close()

		// Create comprehensive schema
		schema := `
		CREATE TABLE articles (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			source TEXT NOT NULL,
			pub_date TIMESTAMP NOT NULL,
			url TEXT NOT NULL UNIQUE,
			title TEXT NOT NULL,
			content TEXT NOT NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			status TEXT DEFAULT 'pending',
			fail_count INTEGER DEFAULT 0,
			last_attempt TIMESTAMP,
			escalated BOOLEAN DEFAULT FALSE,
			composite_score REAL,
			confidence REAL,
			score_source TEXT,
			summary TEXT
		);
		CREATE TABLE llm_scores (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			article_id INTEGER NOT NULL,
			model TEXT NOT NULL,
			score REAL NOT NULL,
			metadata TEXT,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (article_id) REFERENCES articles (id)
		);
		CREATE TABLE feedback (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			article_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			category TEXT NOT NULL,
			comment TEXT,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (article_id) REFERENCES articles (id)
		);
		CREATE TABLE manual_scores (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			article_id INTEGER NOT NULL,
			score REAL NOT NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (article_id) REFERENCES articles (id)
		);`

		_, err := db.Exec(schema)
		assert.NoError(t, err)

		// Insert comprehensive test data
		for i := 0; i < 10; i++ {
			_, err := db.Exec(`
				INSERT INTO articles (source, pub_date, url, title, content, status, composite_score, confidence, summary)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			`,
				"test-source-"+string(rune(i%3+'0')),
				time.Now().Add(-time.Duration(i)*time.Hour),
				"https://example.com/article-"+string(rune(i+'0')),
				"Test Article "+string(rune(i+'0')),
				"Test content for article "+string(rune(i+'0')),
				"analyzed",
				float64(i-5)*0.1, // Mix of positive and negative scores
				0.8+float64(i%3)*0.1,
				"Test summary "+string(rune(i+'0')),
			)
			assert.NoError(t, err)

			// Add LLM scores
			_, err = db.Exec(`
				INSERT INTO llm_scores (article_id, model, score, metadata)
				VALUES (?, ?, ?, ?)
			`, i+1, "gpt-4", float64(i-5)*0.1, `{"confidence": 0.8}`)
			assert.NoError(t, err)

			// Add feedback
			_, err = db.Exec(`
				INSERT INTO feedback (article_id, user_id, category, comment)
				VALUES (?, ?, ?, ?)
			`, i+1, "test-user", "accuracy", "Test feedback "+string(rune(i+'0')))
			assert.NoError(t, err)

			// Add manual scores
			_, err = db.Exec(`
				INSERT INTO manual_scores (article_id, score)
				VALUES (?, ?)
			`, i+1, float64(i-5)*0.1)
			assert.NoError(t, err)
		}

		// Test all handlers with comprehensive scenarios
		testCases := []struct {
			name           string
			method         string
			path           string
			body           string
			expectedStatus int
			handler        gin.HandlerFunc
		}{
			{
				name:           "getArticlesHandler_WithFilters",
				method:         "GET",
				path:           "/articles?source=test-source-0&limit=5&offset=0",
				expectedStatus: 200,
				handler:        getArticlesHandler(db),
			},
			{
				name:           "getArticlesHandler_WithLeaning",
				method:         "GET",
				path:           "/articles?leaning=left",
				expectedStatus: 200,
				handler:        getArticlesHandler(db),
			},
			{
				name:           "getArticleByIDHandler_Valid",
				method:         "GET",
				path:           "/articles/1",
				expectedStatus: 200,
				handler:        getArticleByIDHandler(db),
			},
			{
				name:           "getArticleByIDHandler_NotFound",
				method:         "GET",
				path:           "/articles/999",
				expectedStatus: 404,
				handler:        getArticleByIDHandler(db),
			},
			{
				name:           "biasHandler_Valid",
				method:         "GET",
				path:           "/articles/1/bias",
				expectedStatus: 200,
				handler:        biasHandler(db),
			},
			{
				name:           "biasHandler_NotFound",
				method:         "GET",
				path:           "/articles/999/bias",
				expectedStatus: 404,
				handler:        biasHandler(db),
			},
			{
				name:           "ensembleDetailsHandler_Valid",
				method:         "GET",
				path:           "/articles/1/ensemble",
				expectedStatus: 200,
				handler:        ensembleDetailsHandler(db),
			},
			{
				name:           "ensembleDetailsHandler_NotFound",
				method:         "GET",
				path:           "/articles/999/ensemble",
				expectedStatus: 404,
				handler:        ensembleDetailsHandler(db),
			},
			{
				name:           "manualScoreHandler_Valid",
				method:         "POST",
				path:           "/manual-score/1",
				body:           `{"score": 0.7}`,
				expectedStatus: 200,
				handler:        manualScoreHandler(db),
			},
			{
				name:           "manualScoreHandler_InvalidScore",
				method:         "POST",
				path:           "/manual-score/1",
				body:           `{"score": 2.0}`,
				expectedStatus: 400,
				handler:        manualScoreHandler(db),
			},
			{
				name:           "createArticleHandler_Valid",
				method:         "POST",
				path:           "/articles",
				body:           `{"source": "New Source", "pub_date": "2024-01-01T12:00:00Z", "url": "https://new.com/article", "title": "New Article", "content": "New content"}`,
				expectedStatus: 201,
				handler:        createArticleHandler(db),
			},
			{
				name:           "createArticleHandler_Duplicate",
				method:         "POST",
				path:           "/articles",
				body:           `{"source": "Duplicate", "pub_date": "2024-01-01T12:00:00Z", "url": "https://example.com/article-0", "title": "Duplicate", "content": "Duplicate"}`,
				expectedStatus: 409,
				handler:        createArticleHandler(db),
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				router := gin.New()

				if tc.method == "GET" {
					router.GET("/*path", tc.handler)
				} else {
					router.POST("/*path", tc.handler)
				}

				var req *http.Request
				if tc.body != "" {
					req = httptest.NewRequest(tc.method, tc.path, bytes.NewBuffer([]byte(tc.body)))
					req.Header.Set("Content-Type", "application/json")
				} else {
					req = httptest.NewRequest(tc.method, tc.path, nil)
				}

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				assert.Equal(t, tc.expectedStatus, w.Code, "Test case: %s", tc.name)
			})
		}
	})

	// Test all error response functions
	t.Run("ErrorResponseFunctions", func(t *testing.T) {
		router := gin.New()

		// Test all error types
		router.GET("/validation", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrValidation, "validation error"))
		})
		router.GET("/notfound", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrNotFound, "not found error"))
		})
		router.GET("/conflict", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrConflict, "conflict error"))
		})
		router.GET("/internal", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrInternal, "internal error"))
		})

		// Test success response
		router.GET("/success", func(c *gin.Context) {
			RespondSuccess(c, map[string]string{"message": "success"})
		})

		testCases := []struct {
			path           string
			expectedStatus int
		}{
			{"/validation", 400},
			{"/notfound", 404},
			{"/conflict", 409},
			{"/internal", 500},
			{"/success", 200},
		}

		for _, tc := range testCases {
			req := httptest.NewRequest("GET", tc.path, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			assert.Equal(t, tc.expectedStatus, w.Code)

			var response map[string]interface{}
			jsonErr := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, jsonErr)

			if tc.expectedStatus == 200 {
				assert.True(t, response["success"].(bool))
			} else {
				assert.False(t, response["success"].(bool))
				assert.Contains(t, response, "error")
			}
		}
	})

	// Test utility functions comprehensively
	t.Run("UtilityFunctionsComprehensive", func(t *testing.T) {
		// Test safeLogf with various scenarios
		safeLogf("Simple message")
		safeLogf("Message with string: %s", "test")
		safeLogf("Message with int: %d", 123)
		safeLogf("Message with float: %.2f", 3.14)
		safeLogf("Message with multiple: %s %d %.2f", "test", 123, 3.14)

		// Test getValidArticleID helper
		router := gin.New()
		router.GET("/test/:id", func(c *gin.Context) {
			id, ok := getValidArticleID(c)
			if !ok {
				return
			}
			c.JSON(200, gin.H{"id": id})
		})

		// Test valid IDs
		validIDs := []string{"1", "123", "999999"}
		for _, id := range validIDs {
			req := httptest.NewRequest("GET", "/test/"+id, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			assert.Equal(t, 200, w.Code)
		}

		// Test invalid IDs
		invalidIDs := []string{"invalid", "0", "-1", "abc", "1.5"}
		for _, id := range invalidIDs {
			req := httptest.NewRequest("GET", "/test/"+id, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			assert.Equal(t, 400, w.Code)
		}
	})

	// Test SafeHandler with various scenarios
	t.Run("SafeHandlerComprehensive", func(t *testing.T) {
		router := gin.New()

		// Test normal operation
		router.GET("/normal", SafeHandler(func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		}))

		// Test panic recovery
		router.GET("/panic", SafeHandler(func(c *gin.Context) {
			panic("test panic")
		}))

		// Test error handling
		router.GET("/error", SafeHandler(func(c *gin.Context) {
			RespondError(c, NewAppError(ErrInternal, "test error"))
		}))

		testCases := []struct {
			path           string
			expectedStatus int
		}{
			{"/normal", 200},
			{"/panic", 500},
			{"/error", 500},
		}

		for _, tc := range testCases {
			req := httptest.NewRequest("GET", tc.path, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			assert.Equal(t, tc.expectedStatus, w.Code)
		}
	})
}
