/* Design Tokens - CSS Custom Properties */
/* Global design tokens for colors, typography, spacing, etc. */

:root {
  /* Brand Colors */
  --color-primary: #0056b3;        /* Primary blue - WCAG AA compliant */
  --color-secondary: #6c757d;      /* Secondary gray */
  --color-success: #1e7e34;        /* Success green - WCAG AA compliant (4.5:1) */
  --color-warning: #ffc107;        /* Warning yellow */
  --color-danger: #b02a37;         /* Danger red - WCAG AA compliant (4.5:1) */
  --color-info: #0c7489;          /* Info cyan - WCAG AA compliant (4.5:1) */

  /* Neutral Colors */
  --color-white: #fff;
  --color-light: #f8f9fa;          /* Light background */
  --color-gray-100: #f8f9fa;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;
  --color-dark: #343a40;
  --color-black: #000;

  /* Text Colors */
  --color-text: #333;              /* Primary text color */
  --color-text-muted: #6c757d;     /* Muted text color */
  --color-text-light: #fff;        /* Light text for dark backgrounds */

  /* Background Colors */
  --color-bg: #fff;                /* Primary background */
  --color-bg-light: #f8f9fa;       /* Light background variant */
  --color-bg-dark: #343a40;        /* Dark background */

  /* Bias Indicator Colors */
  --color-bias-left: #0a58ca;      /* Blue for left bias - WCAG AA compliant (4.5:1) */
  --color-bias-center: #6c757d;    /* Gray for center bias */
  --color-bias-right: #b02a37;     /* Red for right bias - WCAG AA compliant (4.5:1) */
  --color-bias-unknown: #ffc107;   /* Yellow for unknown bias */

  /* Typography */
  --font-primary: "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, sans-serif;
  --font-secondary: Georgia, "Times New Roman", Times, serif;
  --font-monospace: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;         /* 12px */
  --font-size-sm: 0.875rem;        /* 14px */
  --font-size-base: 1rem;          /* 16px */
  --font-size-lg: 1.125rem;        /* 18px */
  --font-size-xl: 1.25rem;         /* 20px */
  --font-size-2xl: 1.5rem;         /* 24px */
  --font-size-3xl: 1.875rem;       /* 30px */
  --font-size-4xl: 2.25rem;        /* 36px */
  --font-size-5xl: 3rem;           /* 48px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-base: 1.6;
  --line-height-relaxed: 1.75;
  --line-height-loose: 2;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing Scale */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;        /* 2px */
  --space-1: 0.25rem;           /* 4px */
  --space-1-5: 0.375rem;        /* 6px */
  --space-2: 0.5rem;            /* 8px */
  --space-2-5: 0.625rem;        /* 10px */
  --space-3: 0.75rem;           /* 12px */
  --space-3-5: 0.875rem;        /* 14px */
  --space-4: 1rem;              /* 16px */
  --space-5: 1.25rem;           /* 20px */
  --space-6: 1.5rem;            /* 24px */
  --space-7: 1.75rem;           /* 28px */
  --space-8: 2rem;              /* 32px */
  --space-9: 2.25rem;           /* 36px */
  --space-10: 2.5rem;           /* 40px */
  --space-11: 2.75rem;          /* 44px */
  --space-12: 3rem;             /* 48px */
  --space-14: 3.5rem;           /* 56px */
  --space-16: 4rem;             /* 64px */
  --space-20: 5rem;             /* 80px */
  --space-24: 6rem;             /* 96px */
  --space-28: 7rem;             /* 112px */
  --space-32: 8rem;             /* 128px */

  /* Semantic Spacing */
  --space-xs: var(--space-1);   /* 4px */
  --space-sm: var(--space-2);   /* 8px */
  --space-md: var(--space-4);   /* 16px */
  --space-lg: var(--space-6);   /* 24px */
  --space-xl: var(--space-8);   /* 32px */
  --space-2xl: var(--space-12); /* 48px */
  --space-3xl: var(--space-16); /* 64px */

  /* Border Radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;    /* 2px */
  --border-radius: 0.25rem;        /* 4px */
  --border-radius-md: 0.375rem;    /* 6px */
  --border-radius-lg: 0.5rem;      /* 8px */
  --border-radius-xl: 0.75rem;     /* 12px */
  --border-radius-2xl: 1rem;       /* 16px */
  --border-radius-3xl: 1.5rem;     /* 24px */
  --border-radius-full: 9999px;    /* Fully rounded */

  /* Border Widths */
  --border-width-0: 0;
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;

  /* Container Widths */
  --container-max-width: 1200px;
  --container-sm: 576px;
  --container-md: 768px;
  --container-lg: 992px;
  --container-xl: 1200px;
}
/* Base Styles and Reset */
/* CSS reset/normalize and global typography */

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role="list"],
ol[role="list"] {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

html {
  font-size: 16px; /* Base font size for rem calculations */
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  font-family: var(--font-primary, "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, sans-serif);
  font-size: var(--font-size-base, 1rem);
  line-height: var(--line-height-base, 1.6);
  color: var(--color-text, #333);
  background-color: var(--color-bg, #fff);
  font-weight: var(--font-weight-normal, 400);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Ensure all buttons meet minimum touch target size for accessibility */
button {
  min-height: 44px;
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Typography Elements */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold, 700);
  line-height: var(--line-height-tight, 1.25);
  color: var(--color-text, #333);
  margin-top: 0;
  margin-bottom: var(--space-4, 1rem);
}

/* Heading Sizes - h1 specifically set to 2rem per acceptance criteria */
h1 {
  font-size: 2rem;
  margin-bottom: var(--space-6, 1.5rem);
}

h2 {
  font-size: var(--font-size-3xl, 1.875rem);
  margin-bottom: var(--space-5, 1.25rem);
}

h3 {
  font-size: var(--font-size-2xl, 1.5rem);
  margin-bottom: var(--space-4, 1rem);
}

h4 {
  font-size: var(--font-size-xl, 1.25rem);
  margin-bottom: var(--space-3, 0.75rem);
}

h5 {
  font-size: var(--font-size-lg, 1.125rem);
  margin-bottom: var(--space-3, 0.75rem);
}

h6 {
  font-size: var(--font-size-base, 1rem);
  margin-bottom: var(--space-2, 0.5rem);
}

p {
  margin-bottom: 1rem;
}

/* Link Styles */
a {
  color: #0056b3;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

a:hover {
  color: #004085;
  text-decoration: underline;
}

a:focus {
  outline: 2px solid #0056b3;
  outline-offset: 2px;
}

a:visited {
  color: #0056b3;
}

/* Lists */
ul, ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
}

/* Print styles for clean article printing */
@media print {
  /* Hide navigation and interactive elements */
  nav,
  .nav-links,
  header,
  .search-container,
  .search-form,
  .btn,
  button,
  .sidebar,
  .recent-articles,
  .stats,
  .load-more-section,
  .htmx-indicator,
  .progress-indicator {
    display: none !important;
  }

  /* Optimize page layout for print */
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: none;
    margin: 0;
    padding: 0;
  }

  /* Article content optimization */
  .article-title {
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 12pt;
    page-break-after: avoid;
  }

  .article-meta {
    font-size: 10pt;
    margin-bottom: 12pt;
    border-bottom: 1px solid #ccc;
    padding-bottom: 6pt;
  }

  .article-content {
    font-size: 11pt;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    border: none;
    box-shadow: none;
  }

  .article-content p {
    margin-bottom: 12pt;
    orphans: 3;
    widows: 3;
  }

  .article-content h1,
  .article-content h2,
  .article-content h3 {
    page-break-after: avoid;
    margin-top: 18pt;
    margin-bottom: 6pt;
  }

  /* Bias analysis for print */
  .bias-analysis {
    border: 1px solid #ccc;
    padding: 12pt;
    margin: 12pt 0;
    page-break-inside: avoid;
  }

  .bias-indicator {
    border: 1px solid #666;
    padding: 3pt 6pt;
    background: #f5f5f5;
  }

  /* Ensure links are visible */
  a {
    color: #000;
    text-decoration: underline;
  }

  /* Page breaks */
  .article-header,
  .bias-analysis {
    page-break-inside: avoid;
  }

  /* Remove shadows and borders for clean print */
  .article-header,
  .article-content,
  .bias-analysis {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
/* Layout Helpers */
/* Layout utilities like grid, container widths, responsiveness */

/* Container for centering content */
.container {
  max-width: var(--container-max-width, 1200px);
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

/* Container variants */
.container-fluid {
  max-width: none;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-sm {
  max-width: 576px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-md {
  max-width: 768px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-lg {
  max-width: 992px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-xl {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

/* Grid Layouts */

/* Articles Grid - responsive grid for article listings */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6, 1.5rem);
}

/* Flexbox fallback for browsers that don't support grid */
@supports not (display: grid) {
  .articles-grid {
    display: flex;
    flex-wrap: wrap;
    margin: -0.75rem;
  }

  .articles-grid > * {
    flex: 1 1 300px;
    margin: 0.75rem;
    max-width: calc(33.333% - 1.5rem);
  }
}

/* Responsive breakpoint for mobile */
@media screen and (max-width: 736px) {
  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4, 1rem);
  }

  @supports not (display: grid) {
    .articles-grid {
      margin: -0.5rem;
    }

    .articles-grid > * {
      flex: 1 1 100%;
      margin: 0.5rem;
      max-width: calc(100% - 1rem);
    }
  }
}

/* Two-Column Layout - for main content and sidebar */
/* Flexbox fallback for IE11 and older browsers */
.two-column-layout {
  display: flex;
  gap: var(--space-6, 1.5rem);
}

.two-column-layout > *:first-child {
  flex: 3;
  min-width: 0; /* Prevent flex item from overflowing */
}

.two-column-layout > *:last-child {
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
}

/* Modern grid layout for browsers that support it */
@supports (display: grid) {
  .two-column-layout {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: var(--space-6, 1.5rem);
  }

  .two-column-layout > * {
    margin-bottom: 0;
  }
}

/* Responsive breakpoint for mobile - stack vertically */
@media screen and (max-width: 768px) {
  .two-column-layout {
    flex-direction: column;
    gap: var(--space-4, 1rem);
  }

  .two-column-layout > * {
    flex: none;
  }

  @supports (display: grid) {
    .two-column-layout {
      grid-template-columns: 1fr;
      gap: var(--space-4, 1rem);
    }
  }
}

/* Equal Columns Layout - for dashboard cards and equal-width content */
/* Flexbox fallback for IE11 and older browsers */
.equal-columns-layout {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-6, 1.5rem);
}

.equal-columns-layout > * {
  flex: 1 1 45%;
  margin-bottom: var(--space-6, 1.5rem);
}

/* Modern grid layout for browsers that support it */
@supports (display: grid) {
  .equal-columns-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6, 1.5rem);
  }

  .equal-columns-layout > * {
    margin-bottom: 0;
  }
}

/* Responsive breakpoint for mobile - stack vertically */
@media screen and (max-width: 768px) {
  .equal-columns-layout {
    gap: var(--space-4, 1rem);
  }

  .equal-columns-layout > * {
    flex: 1 0 100%;
    margin-bottom: var(--space-4, 1rem);
  }

  @supports (display: grid) {
    .equal-columns-layout {
      grid-template-columns: 1fr;
      gap: var(--space-4, 1rem);
    }

    .equal-columns-layout > * {
      margin-bottom: 0;
    }
  }
}
/* Reusable UI Components */
/* Styles for buttons, cards, navbars, forms, badges */

/* Navigation Bar Component */
.navbar {
  background-color: var(--color-bg, #fff);
  border-bottom: 1px solid var(--color-gray-300, #dee2e6);
  padding: var(--space-4, 1rem) 0;
  position: relative;
  z-index: 100;
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4, 1rem);
}

.navbar-brand {
  font-size: var(--font-size-xl, 1.25rem);
  font-weight: var(--font-weight-bold, 700);
  color: var(--color-text, #333);
  text-decoration: none;
  margin: 0;
  min-height: 44px; /* Ensure minimum touch target size for accessibility */
  display: flex;
  align-items: center;
  padding: var(--space-2, 0.5rem) 0;
}

.navbar-brand:hover {
  color: var(--color-primary, #007bff);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6, 1.5rem);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav a {
  color: var(--color-text, #333);
  text-decoration: none;
  font-weight: var(--font-weight-medium, 500);
  padding: var(--space-3, 0.75rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius, 0.25rem);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
  min-height: 44px; /* Ensure minimum touch target size for accessibility */
  display: flex;
  align-items: center;
}

.navbar-nav a:hover,
.navbar-nav a:focus {
  color: var(--color-primary, #007bff);
  background-color: var(--color-gray-100, #f8f9fa);
  text-decoration: none;
}

.navbar-nav a.active {
  color: var(--color-primary, #007bff);
  font-weight: var(--font-weight-semibold, 600);
}

/* Responsive navbar */
@media screen and (max-width: 768px) {
  .navbar .container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3, 0.75rem);
  }

  .navbar-nav {
    width: 100%;
    justify-content: center;
    gap: var(--space-4, 1rem);
  }
}

/* Button Component */
.btn {
  display: inline-block;
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  margin: 0;
  font-family: inherit;
  font-size: var(--font-size-base, 1rem);
  font-weight: var(--font-weight-medium, 500);
  line-height: var(--line-height-base, 1.6);
  text-align: center;
  text-decoration: none;
  min-height: 44px; /* Ensure minimum touch target size for accessibility */
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: var(--color-gray-100, #f8f9fa);
  border: var(--border-width, 1px) solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  color: var(--color-text, #333);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
  color: var(--color-text, #333);
  background-color: var(--color-gray-200, #e9ecef);
  border-color: var(--color-gray-400, #ced4da);
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:active {
  background-color: var(--color-gray-300, #dee2e6);
  border-color: var(--color-gray-400, #ced4da);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button sizes */
.btn-sm {
  padding: var(--space-2-5, 0.625rem) var(--space-3, 0.75rem);
  font-size: var(--font-size-sm, 0.875rem);
  border-radius: var(--border-radius-sm, 0.125rem);
  min-height: 44px; /* Ensure minimum touch target size for accessibility */
}

.btn-lg {
  padding: var(--space-3, 0.75rem) var(--space-5, 1.25rem);
  font-size: var(--font-size-lg, 1.125rem);
  border-radius: var(--border-radius-lg, 0.5rem);
}

/* Button Color Variants */
.btn-primary {
  color: var(--color-white, #fff);
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:hover {
  color: var(--color-white, #fff);
  background-color: #0056b3;
  border-color: #004085;
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-primary:active {
  color: var(--color-white, #fff);
  background-color: #004085;
  border-color: #003d82;
}

.btn-secondary {
  color: var(--color-white, #fff);
  background-color: var(--color-gray-600, #6c757d);
  border-color: var(--color-gray-600, #6c757d);
}

.btn-secondary:hover {
  color: var(--color-white, #fff);
  background-color: #545b62;
  border-color: #4e555b;
}

.btn-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-secondary:active {
  color: var(--color-white, #fff);
  background-color: #4e555b;
  border-color: #47525d;
}

.btn-success {
  color: var(--color-white, #fff);
  background-color: var(--color-success, #1e7e34);
  border-color: var(--color-success, #1e7e34);
}

.btn-success:hover {
  color: var(--color-white, #fff);
  background-color: #1c7430;
  border-color: #1a6e2d;
}

.btn-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(30, 126, 52, 0.5);
}

.btn-success:active {
  color: var(--color-white, #fff);
  background-color: #1a6e2d;
  border-color: #18682a;
}

.btn-warning {
  color: var(--color-gray-900, #212529);
  background-color: var(--color-warning, #ffc107);
  border-color: var(--color-warning, #ffc107);
}

.btn-warning:hover {
  color: var(--color-gray-900, #212529);
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-warning:active {
  color: var(--color-gray-900, #212529);
  background-color: #d39e00;
  border-color: #c69500;
}

.btn-danger {
  color: var(--color-white, #fff);
  background-color: var(--color-danger, #b02a37);
  border-color: var(--color-danger, #b02a37);
}

.btn-danger:hover {
  color: var(--color-white, #fff);
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-danger:active {
  color: var(--color-white, #fff);
  background-color: #bd2130;
  border-color: #b21f2d;
}

.btn-info {
  color: var(--color-white, #fff);
  background-color: var(--color-info, #17a2b8);
  border-color: var(--color-info, #17a2b8);
}

.btn-info:hover {
  color: var(--color-white, #fff);
  background-color: #0a5d6e;
  border-color: #095560;
}

.btn-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(12, 116, 137, 0.5);
}

.btn-info:active {
  color: var(--color-white, #fff);
  background-color: #095560;
  border-color: #084d56;
}

/* Article Cards */
.article-item,
.article-card {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, rgba(210, 215, 217, 0.75));
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  transition: all 0.2s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.article-item:hover,
.article-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary, #007bff);
}

.article-title {
  margin: 0 0 var(--space-2, 0.5rem) 0;
  font-size: var(--font-size-lg, 1.125rem);
  line-height: var(--line-height-snug, 1.375);
  font-weight: var(--font-weight-semibold, 600);
}

.article-title a {
  color: var(--color-text, #333);
  text-decoration: none;
}

.article-title a:hover {
  color: var(--color-primary, #007bff);
  text-decoration: underline;
}

.article-meta {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-2, 0.5rem) 0;
  flex-grow: 1;
}

.article-actions {
  margin-top: auto;
  padding-top: var(--space-4, 1rem);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Bias Indicators */
.bias-indicator {
  display: inline-block;
  padding: var(--space-1, 0.25rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius-md, 0.375rem);
  font-size: var(--font-size-xs, 0.75rem);
  font-weight: var(--font-weight-semibold, 600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.bias-left {
  background-color: rgba(10, 88, 202, 0.1);
  color: var(--color-bias-left, #0a58ca);
  border: 1px solid rgba(10, 88, 202, 0.25);
}

.bias-center {
  background-color: rgba(108, 117, 125, 0.15);
  color: #495057;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.bias-right {
  background-color: rgba(176, 42, 55, 0.1);
  color: var(--color-bias-right, #b02a37);
  border: 1px solid rgba(176, 42, 55, 0.25);
}

/* Filter Section */
.filter-section {
  background: var(--color-gray-100, #f8f9fa);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  margin-bottom: var(--space-8, 2rem);
}

/* Results Summary */
.results-summary {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-4, 1rem) 0 var(--space-8, 2rem) 0;
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  background: rgba(248, 249, 250, 0.5);
  border-radius: var(--border-radius-md, 0.375rem);
  border-left: 3px solid var(--color-primary, #007bff);
}

/* HTMX Loading States */
.htmx-indicator {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
  opacity: 1;
}

.htmx-request.htmx-indicator {
  opacity: 1;
}

/* Responsive adjustments */
@media screen and (max-width: 736px) {
  .filter-section {
    padding: var(--space-4, 1rem);
  }
}
/* Utility Classes */
/* Small utility classes for common styling needs */

/* Loading and display utilities */
.loading-hidden {
  display: none;
  margin-left: 10px;
}



.filter-info {
  margin-left: 10px;
}

/* Progress and button loading states */
.progress-hidden {
  display: none;
  margin: 15px 0;
}

.btn-loading-hidden {
  display: none;
}

/* Common utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Margin utilities */
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-4 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-5 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Padding utilities */
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Display utilities */
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

/* Flexbox utilities */
.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}



/* Load More Section */
.load-more-section {
  margin: var(--space-5, 1.25rem) 0;
  text-align: center;
}

/* Button Spacing */
.btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3, 0.75rem);
  margin-bottom: var(--space-4, 1rem);
}

.btn-group .btn {
  flex: 0 0 auto;
}

/* Admin Dashboard Styles */
.admin-dashboard {
  margin-top: var(--space-6, 1.5rem);
}

.dashboard-stats {
  margin-bottom: var(--space-6, 1.5rem);
}

.dashboard-card {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: var(--space-5, 1.25rem);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-gray-800, #495057);
  font-size: var(--font-size-lg, 1.125rem);
  font-weight: 600;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2, 0.5rem) 0;
  border-bottom: 1px solid var(--color-gray-200, #e9ecef);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-value {
  font-weight: 600;
  color: var(--color-primary, #0056b3);
}

/* System Status */
.system-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4, 1rem);
  margin-bottom: var(--space-6, 1.5rem);
}

.status-item {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: var(--space-4, 1rem);
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-item h4 {
  margin: 0 0 var(--space-2, 0.5rem) 0;
  font-size: var(--font-size-base, 1rem);
  font-weight: 600;
}

.status-item p {
  margin: 0;
  font-size: var(--font-size-sm, 0.875rem);
}

.status-good {
  border-left: 4px solid var(--color-success, #1e7e34);
}

.status-warning {
  border-left: 4px solid var(--color-warning, #ffc107);
}

.status-error {
  border-left: 4px solid var(--color-danger, #b02a37);
}

/* Admin Controls */
.admin-controls {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: var(--space-5, 1.25rem);
  margin-bottom: var(--space-6, 1.5rem);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-controls > h3 {
  margin: 0 0 var(--space-5, 1.25rem) 0;
  color: var(--color-gray-800, #495057);
  font-size: var(--font-size-xl, 1.25rem);
  font-weight: 600;
  border-bottom: 2px solid var(--color-gray-200, #e9ecef);
  padding-bottom: var(--space-3, 0.75rem);
}

.control-section {
  margin-bottom: var(--space-5, 1.25rem);
}

.control-section:last-child {
  margin-bottom: 0;
}

.control-section h3 {
  margin: 0 0 var(--space-3, 0.75rem) 0;
  color: var(--color-gray-700, #6c757d);
  font-size: var(--font-size-lg, 1.125rem);
  font-weight: 500;
}

/* Bias Distribution */
.bias-distribution {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4, 1rem);
  text-align: center;
}

.bias-stat {
  padding: var(--space-3, 0.75rem);
  border-radius: var(--border-radius, 0.25rem);
  background: var(--color-gray-50, #f8f9fa);
}

.bias-stat .value {
  font-size: var(--font-size-2xl, 1.5rem);
  font-weight: 700;
  margin-bottom: var(--space-1, 0.25rem);
}

.bias-stat .label {
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-gray-700, #495057);
}

.bias-left .value {
  color: #1e40af;
}

.bias-center .value {
  color: var(--color-gray-700, #374151);
}

.bias-right .value {
  color: #dc2626;
}

/* Recent Activity */
.recent-activity {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: var(--space-5, 1.25rem);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-gray-800, #495057);
  font-size: var(--font-size-lg, 1.125rem);
  font-weight: 600;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3, 0.75rem) 0;
  border-bottom: 1px solid var(--color-gray-200, #e9ecef);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-gray-600, #6c757d);
}

/* Responsive Design for Admin Dashboard */
@media (max-width: 768px) {
  .system-status {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3, 0.75rem);
  }

  .equal-columns-layout {
    flex-direction: column;
  }

  .bias-distribution {
    grid-template-columns: 1fr;
    gap: var(--space-3, 0.75rem);
  }

  .btn-group {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-group .btn {
    width: 100%;
    margin-bottom: var(--space-2, 0.5rem);
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2, 0.5rem);
  }
}

@media (max-width: 480px) {
  .system-status {
    grid-template-columns: 1fr;
  }

  .dashboard-card,
  .admin-controls,
  .recent-activity {
    padding: var(--space-4, 1rem);
  }

  .control-section h4 {
    font-size: var(--font-size-base, 1rem);
  }
}

/* Source Management Components */
.source-management-section {
  margin: var(--space-6, 1.5rem) 0;
  padding: var(--space-6, 1.5rem);
  background: var(--color-bg, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.375rem);
}

.source-management-container {
  width: 100%;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4, 1rem);
  padding-bottom: var(--space-3, 0.75rem);
  border-bottom: 1px solid var(--color-gray-300, #dee2e6);
}

.source-header h4 {
  margin: 0;
  color: var(--color-text, #333);
}

.source-form-container {
  margin-bottom: var(--space-4, 1rem);
}

.source-form {
  background: var(--color-gray-50, #f8f9fa);
  padding: var(--space-4, 1rem);
  border-radius: var(--border-radius, 0.375rem);
  border: 1px solid var(--color-gray-300, #dee2e6);
}

.source-form h5 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-text, #333);
}

.source-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3, 0.75rem);
  list-style: none;
  margin: 0;
  padding: 0;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-4, 1rem);
  background: var(--color-bg, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.375rem);
  transition: box-shadow 0.2s ease;
}

.source-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.source-info {
  flex: 1;
  margin-right: var(--space-4, 1rem);
}

.source-name {
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
  margin-bottom: var(--space-2, 0.5rem);
}

.source-name strong {
  font-size: var(--font-size-lg, 1.125rem);
  color: var(--color-text, #333);
}

.source-url {
  margin-bottom: var(--space-2, 0.5rem);
}

.source-url a {
  color: var(--color-primary, #007bff);
  text-decoration: none;
  font-size: var(--font-size-sm, 0.875rem);
  word-break: break-all;
}

.source-url a:hover {
  text-decoration: underline;
}

.source-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3, 0.75rem);
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-gray-600, #6c757d);
}

.source-actions {
  display: flex;
  gap: var(--space-2, 0.5rem);
  flex-shrink: 0;
}

.error-streak {
  color: var(--color-danger, #dc3545);
  font-weight: var(--font-weight-semibold, 600);
}

.no-sources {
  text-align: center;
  padding: var(--space-8, 2rem);
  color: var(--color-gray-600, #6c757d);
}

/* Source Stats Modal */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: var(--color-bg, #fff);
  margin: 5% auto;
  padding: var(--space-6, 1.5rem);
  border-radius: var(--border-radius, 0.375rem);
  width: 90%;
  max-width: 600px;
  position: relative;
}

.close {
  position: absolute;
  right: var(--space-4, 1rem);
  top: var(--space-4, 1rem);
  color: var(--color-gray-500, #adb5bd);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: var(--color-text, #333);
}

.source-stats h5 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-text, #333);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3, 0.75rem);
  margin-bottom: var(--space-4, 1rem);
}

.source-stats .stat-item {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2, 0.5rem);
  background: var(--color-gray-50, #f8f9fa);
  border-radius: var(--border-radius, 0.375rem);
  border-bottom: none;
}

.source-stats .stat-label {
  font-weight: var(--font-weight-semibold, 600);
  color: var(--color-gray-700, #495057);
}

.source-stats .stat-value {
  color: var(--color-text, #333);
  font-weight: normal;
}

.status-enabled {
  color: var(--color-success, #28a745);
  font-weight: var(--font-weight-semibold, 600);
}

.status-disabled {
  color: var(--color-warning, #ffc107);
  font-weight: var(--font-weight-semibold, 600);
}

.stats-actions {
  text-align: center;
  padding-top: var(--space-4, 1rem);
  border-top: 1px solid var(--color-gray-300, #dee2e6);
}

/* Badge styles for source management */
.badge-rss {
  background-color: var(--color-info, #17a2b8);
  color: white;
}

.badge-telegram {
  background-color: #08c;
  color: white;
}

.badge-twitter {
  background-color: #1da1f2;
  color: white;
}

.badge-reddit {
  background-color: #ff4500;
  color: white;
}

.badge-left {
  background-color: var(--color-primary, #007bff);
  color: white;
}

.badge-center {
  background-color: var(--color-secondary, #6c757d);
  color: white;
}

.badge-right {
  background-color: var(--color-danger, #dc3545);
  color: white;
}

.badge-disabled {
  background-color: var(--color-warning, #ffc107);
  color: var(--color-text, #333);
}
