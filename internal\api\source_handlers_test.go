package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/alexand<PERSON>-sa<PERSON><PERSON>/BalancedNewsGo/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	_ "modernc.org/sqlite"
)

var (
	ginTestModeOnceSource sync.Once
)

// TestDB represents a test database with cleanup functionality for source tests
type SourceTestDB struct {
	*sqlx.DB
	cleanup func()
}

// setupSourceTestDB creates a test database with proper schema and cleanup for source tests
func setupSourceTestDB(t *testing.T) *SourceTestDB {
	// Use in-memory SQLite database for tests
	dbConn, err := sqlx.Open("sqlite", ":memory:")
	assert.NoError(t, err, "Failed to create test database")

	// Apply schema
	schema := `
	CREATE TABLE IF NOT EXISTS sources (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL UNIQUE,
		channel_type TEXT NOT NULL DEFAULT 'rss',
		feed_url TEXT NOT NULL,
		category TEXT NOT NULL,
		enabled BOOLEAN NOT NULL DEFAULT 1,
		default_weight REAL NOT NULL DEFAULT 1.0,
		last_fetched_at TIMESTAMP,
		error_streak INTEGER NOT NULL DEFAULT 0,
		metadata TEXT,
		created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
	);

	CREATE TABLE IF NOT EXISTS articles (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		source TEXT NOT NULL,
		pub_date TIMESTAMP NOT NULL,
		url TEXT NOT NULL UNIQUE,
		title TEXT NOT NULL,
		content TEXT NOT NULL,
		created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
		status TEXT DEFAULT 'pending',
		fail_count INTEGER DEFAULT 0,
		last_attempt TIMESTAMP,
		escalated BOOLEAN DEFAULT FALSE,
		composite_score REAL,
		confidence REAL,
		score_source TEXT
	);
	`

	_, err = dbConn.Exec(schema)
	assert.NoError(t, err, "Failed to apply test schema")

	cleanup := func() {
		if err := dbConn.Close(); err != nil {
			t.Logf("Warning: Failed to close test database: %v", err)
		}
	}

	t.Cleanup(cleanup)

	return &SourceTestDB{
		DB:      dbConn,
		cleanup: cleanup,
	}
}

// Helper functions for creating test sources
func insertTestSourcesForSourceHandler(db *SourceTestDB, count int) []int64 {
	var sourceIDs []int64

	categories := []string{"left", "center", "right"}
	channelTypes := []string{"rss", "api"}

	for i := 0; i < count; i++ {
		enabled := i%2 == 0 // Alternate enabled/disabled
		result, err := db.DB.Exec(`
			INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
			VALUES (?, ?, ?, ?, ?, ?)
		`,
			fmt.Sprintf("Test Source %d", i),
			channelTypes[i%len(channelTypes)],
			fmt.Sprintf("https://example%d.com/feed.xml", i),
			categories[i%len(categories)],
			enabled,
			1.0+float64(i)*0.1,
		)
		if err != nil {
			panic(err)
		}

		id, _ := result.LastInsertId()
		sourceIDs = append(sourceIDs, id)
	}

	return sourceIDs
}

// TestGetSourcesHandler tests the sources listing handler with comprehensive scenarios
func TestGetSourcesHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB) int
		queryParams    string
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful_sources_listing_no_filters",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 5))
			},
			queryParams:    "",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 5, len(sources), "Should return all 5 sources")

				// Check pagination info
				assert.Equal(t, float64(5), data["total"], "Total should be 5")
				assert.Equal(t, float64(50), data["limit"], "Default limit should be 50")
				assert.Equal(t, float64(0), data["offset"], "Default offset should be 0")
			},
		},
		{
			name: "sources_listing_with_enabled_filter",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 6)) // 3 enabled, 3 disabled
			},
			queryParams:    "enabled=true",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 3, len(sources), "Should return only enabled sources")

				// Verify all returned sources are enabled
				for _, sourceInterface := range sources {
					source := sourceInterface.(map[string]interface{})
					assert.True(t, source["enabled"].(bool), "All returned sources should be enabled")
				}
			},
		},
		{
			name: "sources_listing_with_disabled_filter",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 6)) // 3 enabled, 3 disabled
			},
			queryParams:    "enabled=false",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 3, len(sources), "Should return only disabled sources")

				// Verify all returned sources are disabled
				for _, sourceInterface := range sources {
					source := sourceInterface.(map[string]interface{})
					assert.False(t, source["enabled"].(bool), "All returned sources should be disabled")
				}
			},
		},
		{
			name: "sources_listing_with_category_filter",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 9)) // 3 of each category
			},
			queryParams:    "category=left",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 3, len(sources), "Should return only left category sources")

				// Verify all returned sources are left category
				for _, sourceInterface := range sources {
					source := sourceInterface.(map[string]interface{})
					assert.Equal(t, "left", source["category"], "All returned sources should be left category")
				}
			},
		},
		{
			name: "sources_listing_with_channel_type_filter",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 8)) // 4 rss, 4 api
			},
			queryParams:    "channel_type=rss",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 4, len(sources), "Should return only RSS sources")

				// Verify all returned sources are RSS
				for _, sourceInterface := range sources {
					source := sourceInterface.(map[string]interface{})
					assert.Equal(t, "rss", source["channel_type"], "All returned sources should be RSS")
				}
			},
		},
		{
			name: "sources_listing_with_pagination",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 25))
			},
			queryParams:    "limit=10&offset=5",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 10, len(sources), "Should return 10 sources per page")
				assert.Equal(t, float64(10), data["limit"], "Limit should be 10")
				assert.Equal(t, float64(5), data["offset"], "Offset should be 5")
			},
		},
		{
			name: "sources_listing_with_max_limit_enforcement",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 150))
			},
			queryParams:    "limit=200", // Should be capped at 100
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.LessOrEqual(t, len(sources), 100, "Should enforce max limit of 100")
				assert.Equal(t, float64(100), data["limit"], "Limit should be capped at 100")
			},
		},
		{
			name: "sources_listing_empty_database",
			setupDB: func(db *SourceTestDB) int {
				return 0 // No sources
			},
			queryParams:    "",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				sources := data["sources"].([]interface{})
				assert.Equal(t, 0, len(sources), "Should return empty array")
				assert.Equal(t, float64(0), data["total"], "Total should be 0")
			},
		},
		{
			name: "sources_listing_invalid_enabled_parameter",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 3))
			},
			queryParams:    "enabled=invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid enabled parameter")
			},
		},
		{
			name: "sources_listing_invalid_category",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 3))
			},
			queryParams:    "category=invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid category")
			},
		},
		{
			name: "sources_listing_invalid_channel_type",
			setupDB: func(db *SourceTestDB) int {
				return len(insertTestSourcesForSourceHandler(db, 3))
			},
			queryParams:    "channel_type=invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid channel type")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			expectedCount := tt.setupDB(testDB)
			t.Logf("Created %d sources for test", expectedCount)

			// Create handler
			handler := getSourcesHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.GET("/api/sources", handler)

			// Create request with query parameters
			url := "/api/sources"
			if tt.queryParams != "" {
				url += "?" + tt.queryParams
			}
			req := httptest.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)
		})
	}
}

// TestCreateSourceHandler tests the source creation handler with comprehensive scenarios
func TestCreateSourceHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB)
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful_source_creation",
			setupDB: func(db *SourceTestDB) {
				// No setup needed for successful creation
			},
			requestBody: models.CreateSourceRequest{
				Name:          "Test News Source",
				ChannelType:   "rss",
				FeedURL:       "https://example.com/feed.xml",
				Category:      "center",
				DefaultWeight: 1.5,
			},
			expectedStatus: http.StatusCreated,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				assert.Equal(t, "Test News Source", data["name"], "Name should match")
				assert.Equal(t, "rss", data["channel_type"], "Channel type should match")
				assert.Equal(t, "https://example.com/feed.xml", data["feed_url"], "Feed URL should match")
				assert.Equal(t, "center", data["category"], "Category should match")
				assert.Equal(t, 1.5, data["default_weight"], "Default weight should match")
				assert.True(t, data["enabled"].(bool), "New sources should be enabled by default")
				assert.NotNil(t, data["id"], "ID should be set")
				assert.NotNil(t, data["created_at"], "Created at should be set")
				assert.NotNil(t, data["updated_at"], "Updated at should be set")
			},
		},
		{
			name: "successful_source_creation_with_default_weight",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody: models.CreateSourceRequest{
				Name:        "Test Source No Weight",
				ChannelType: "rss",
				FeedURL:     "https://example.com/feed2.xml",
				Category:    "left",
				// DefaultWeight not provided - should default to 1.0
			},
			expectedStatus: http.StatusCreated,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})
				assert.Equal(t, 1.0, data["default_weight"], "Default weight should be 1.0 when not provided")
			},
		},
		{
			name: "source_creation_duplicate_name",
			setupDB: func(db *SourceTestDB) {
				// Insert existing source with same name
				_, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Duplicate Source", "rss", "https://existing.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
			},
			requestBody: models.CreateSourceRequest{
				Name:        "Duplicate Source",
				ChannelType: "rss",
				FeedURL:     "https://new.com/feed.xml",
				Category:    "right",
			},
			expectedStatus: http.StatusConflict,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "already exists")
			},
		},
		{
			name: "source_creation_invalid_json",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid request body")
			},
		},
		{
			name: "source_creation_missing_required_fields",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody: models.CreateSourceRequest{
				Name: "Incomplete Source",
				// Missing required fields: ChannelType, FeedURL, Category
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
			},
		},
		{
			name: "source_creation_invalid_category",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody: models.CreateSourceRequest{
				Name:        "Invalid Category Source",
				ChannelType: "rss",
				FeedURL:     "https://example.com/feed.xml",
				Category:    "invalid_category",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				// Gin validation error message contains "oneof" for invalid category
				assert.Contains(t, errorObj["message"], "oneof")
			},
		},
		{
			name: "source_creation_invalid_channel_type",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody: models.CreateSourceRequest{
				Name:        "Invalid Channel Source",
				ChannelType: "invalid_channel",
				FeedURL:     "https://example.com/feed.xml",
				Category:    "center",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "invalid channel type")
			},
		},
		{
			name: "source_creation_negative_weight",
			setupDB: func(db *SourceTestDB) {
				// No setup needed
			},
			requestBody: models.CreateSourceRequest{
				Name:          "Negative Weight Source",
				ChannelType:   "rss",
				FeedURL:       "https://example.com/feed.xml",
				Category:      "center",
				DefaultWeight: -1.0,
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "non-negative")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			tt.setupDB(testDB)

			// Create handler
			handler := createSourceHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.POST("/api/sources", handler)

			// Prepare request body
			var requestBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				requestBody = []byte(str)
			} else {
				requestBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err, "Failed to marshal request body")
			}

			// Create request
			req := httptest.NewRequest("POST", "/api/sources", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)
		})
	}
}

// TestGetSourceByIDHandler tests the get source by ID handler
func TestGetSourceByIDHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB) int64
		sourceID       string
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful_source_retrieval",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Test Source", "rss", "https://example.com/feed.xml", "center", true, 1.5)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})
				// The getSourceByIDHandler returns SourceWithStats, which has a "source" field
				if sourceField, exists := data["source"]; exists && sourceField != nil {
					source := sourceField.(map[string]interface{})
					assert.Equal(t, "Test Source", source["name"], "Name should match")
					assert.Equal(t, "rss", source["channel_type"], "Channel type should match")
					assert.Equal(t, "https://example.com/feed.xml", source["feed_url"], "Feed URL should match")
					assert.Equal(t, "center", source["category"], "Category should match")
					assert.True(t, source["enabled"].(bool), "Should be enabled")
					assert.Equal(t, 1.5, source["default_weight"], "Default weight should match")
				} else {
					// If no nested source field, data itself is the source
					assert.Equal(t, "Test Source", data["name"], "Name should match")
					assert.Equal(t, "rss", data["channel_type"], "Channel type should match")
					assert.Equal(t, "https://example.com/feed.xml", data["feed_url"], "Feed URL should match")
					assert.Equal(t, "center", data["category"], "Category should match")
					assert.True(t, data["enabled"].(bool), "Should be enabled")
					assert.Equal(t, 1.5, data["default_weight"], "Default weight should match")
				}
			},
		},
		{
			name: "source_not_found",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No sources created
			},
			sourceID:       "999",
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "not found")
			},
		},
		{
			name: "invalid_source_id",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No setup needed
			},
			sourceID:       "invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid source ID")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			expectedID := tt.setupDB(testDB)
			t.Logf("Created source with ID: %d", expectedID)

			// Create handler
			handler := getSourceByIDHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.GET("/api/sources/:id", handler)

			// Create request
			req := httptest.NewRequest("GET", "/api/sources/"+tt.sourceID, nil)
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)
		})
	}
}

// TestUpdateSourceHandler tests the source update handler
func TestUpdateSourceHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB) int64
		sourceID       string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful_source_update",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Original Source", "rss", "https://original.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID: "1",
			requestBody: models.UpdateSourceRequest{
				Name:          stringPtr("Updated Source"),
				Category:      stringPtr("left"),
				DefaultWeight: float64Ptr(2.0),
				Enabled:       boolPtr(false),
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				assert.Equal(t, "Updated Source", data["name"], "Name should be updated")
				assert.Equal(t, "left", data["category"], "Category should be updated")
				assert.Equal(t, 2.0, data["default_weight"], "Default weight should be updated")
				assert.False(t, data["enabled"].(bool), "Should be disabled")
			},
		},
		{
			name: "partial_source_update",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Partial Update Source", "rss", "https://partial.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID: "1",
			requestBody: models.UpdateSourceRequest{
				Name: stringPtr("Partially Updated Source"),
				// Only updating name, other fields should remain unchanged
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				assert.Equal(t, "Partially Updated Source", data["name"], "Name should be updated")
				assert.Equal(t, "center", data["category"], "Category should remain unchanged")
				assert.Equal(t, 1.0, data["default_weight"], "Default weight should remain unchanged")
				assert.True(t, data["enabled"].(bool), "Should remain enabled")
			},
		},
		{
			name: "update_source_not_found",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No sources created
			},
			sourceID: "999",
			requestBody: models.UpdateSourceRequest{
				Name: stringPtr("Non-existent Source"),
			},
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "not found")
			},
		},
		{
			name: "update_invalid_source_id",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No setup needed
			},
			sourceID: "invalid",
			requestBody: models.UpdateSourceRequest{
				Name: stringPtr("Invalid ID Source"),
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid source ID")
			},
		},
		{
			name: "update_no_changes_provided",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "No Changes Source", "rss", "https://nochanges.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID:       "1",
			requestBody:    models.UpdateSourceRequest{}, // Empty update
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "No updates provided")
			},
		},
		{
			name: "update_invalid_category",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Invalid Category Source", "rss", "https://invalid.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID: "1",
			requestBody: models.UpdateSourceRequest{
				Category: stringPtr("invalid_category"),
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "invalid category")
			},
		},
		{
			name: "update_negative_weight",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Negative Weight Source", "rss", "https://negative.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID: "1",
			requestBody: models.UpdateSourceRequest{
				DefaultWeight: float64Ptr(-1.0),
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "non-negative")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			expectedID := tt.setupDB(testDB)
			t.Logf("Created source with ID: %d", expectedID)

			// Create handler
			handler := updateSourceHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.PUT("/api/sources/:id", handler)

			// Prepare request body
			requestBody, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err, "Failed to marshal request body")

			// Create request
			req := httptest.NewRequest("PUT", "/api/sources/"+tt.sourceID, bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)
		})
	}
}

// Helper functions for pointer creation
func stringPtr(s string) *string {
	return &s
}

func float64Ptr(f float64) *float64 {
	return &f
}

func boolPtr(b bool) *bool {
	return &b
}

// TestDeleteSourceHandler tests the source deletion (soft delete) handler
func TestDeleteSourceHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB) int64
		sourceID       string
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
		verifyDB       func(*testing.T, *SourceTestDB, int64)
	}{
		{
			name: "successful_source_deletion",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Source to Delete", "rss", "https://delete.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(string)
				assert.Contains(t, data, "disabled successfully", "Should contain success message")
			},
			verifyDB: func(t *testing.T, db *SourceTestDB, sourceID int64) {
				// Verify source is soft deleted (disabled)
				var enabled bool
				err := db.DB.QueryRow("SELECT enabled FROM sources WHERE id = ?", sourceID).Scan(&enabled)
				assert.NoError(t, err, "Should be able to query source")
				assert.False(t, enabled, "Source should be disabled after deletion")
			},
		},
		{
			name: "delete_source_not_found",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No sources created
			},
			sourceID:       "999",
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "not found")
			},
			verifyDB: func(t *testing.T, db *SourceTestDB, sourceID int64) {
				// No verification needed for non-existent source
			},
		},
		{
			name: "delete_invalid_source_id",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No setup needed
			},
			sourceID:       "invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid source ID")
			},
			verifyDB: func(t *testing.T, db *SourceTestDB, sourceID int64) {
				// No verification needed for invalid ID
			},
		},
		{
			name: "delete_already_disabled_source",
			setupDB: func(db *SourceTestDB) int64 {
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Already Disabled Source", "rss", "https://disabled.com/feed.xml", "center", false, 1.0)
				assert.NoError(t, err)
				id, _ := result.LastInsertId()
				return id
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(string)
				assert.Contains(t, data, "disabled successfully", "Should contain success message")
			},
			verifyDB: func(t *testing.T, db *SourceTestDB, sourceID int64) {
				// Verify source remains disabled
				var enabled bool
				err := db.DB.QueryRow("SELECT enabled FROM sources WHERE id = ?", sourceID).Scan(&enabled)
				assert.NoError(t, err, "Should be able to query source")
				assert.False(t, enabled, "Source should remain disabled")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			expectedID := tt.setupDB(testDB)
			t.Logf("Created source with ID: %d", expectedID)

			// Create handler
			handler := deleteSourceHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.DELETE("/api/sources/:id", handler)

			// Create request
			req := httptest.NewRequest("DELETE", "/api/sources/"+tt.sourceID, nil)
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)

			// Verify database state
			tt.verifyDB(t, testDB, expectedID)
		})
	}
}

// TestGetSourceStatsHandler tests the source statistics handler
func TestGetSourceStatsHandler(t *testing.T) {
	ginTestModeOnceSource.Do(func() {
		gin.SetMode(gin.TestMode)
	})

	tests := []struct {
		name           string
		setupDB        func(*SourceTestDB) int64
		sourceID       string
		expectedStatus int
		checkResponse  func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful_stats_retrieval",
			setupDB: func(db *SourceTestDB) int64 {
				// Create a source
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Stats Source", "rss", "https://stats.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				sourceID, _ := result.LastInsertId()

				// Add some articles for this source
				for i := 0; i < 5; i++ {
					_, err = db.DB.Exec(`
						INSERT INTO articles (source, pub_date, url, title, content, status, composite_score, created_at)
						VALUES (?, ?, ?, ?, ?, ?, ?, ?)
					`,
						"Stats Source",
						time.Now().Add(-time.Duration(i)*time.Hour),
						fmt.Sprintf("https://stats.com/article-%d", i),
						fmt.Sprintf("Article %d", i),
						"Test content",
						"analyzed",
						0.1*float64(i),
						time.Now().Add(-time.Duration(i)*time.Hour),
					)
					assert.NoError(t, err)
				}

				return sourceID
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				// Verify stats structure (currently placeholder implementation)
				assert.Equal(t, float64(1), data["source_id"], "Source ID should match")
				assert.Equal(t, float64(0), data["article_count"], "Article count should be 0 (placeholder)")
				assert.Nil(t, data["avg_score"], "Avg score should be nil (placeholder)")
				assert.NotNil(t, data["computed_at"], "Computed at should be set")
			},
		},
		{
			name: "stats_source_not_found",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No sources created
			},
			sourceID:       "999",
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "not found")
			},
		},
		{
			name: "stats_invalid_source_id",
			setupDB: func(db *SourceTestDB) int64 {
				return 0 // No setup needed
			},
			sourceID:       "invalid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.False(t, response["success"].(bool), "Success should be false")
				assert.Contains(t, response, "error", "Should contain error field")
				errorObj := response["error"].(map[string]interface{})
				assert.Contains(t, errorObj["message"], "Invalid source ID")
			},
		},
		{
			name: "stats_empty_source",
			setupDB: func(db *SourceTestDB) int64 {
				// Create a source with no articles
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Empty Source", "rss", "https://empty.com/feed.xml", "center", true, 1.0)
				assert.NoError(t, err)
				sourceID, _ := result.LastInsertId()
				return sourceID
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				// Verify stats structure for empty source
				assert.Equal(t, float64(1), data["source_id"], "Source ID should match")
				assert.Equal(t, float64(0), data["article_count"], "Article count should be 0")
				assert.Nil(t, data["avg_score"], "Avg score should be nil for empty source")
				assert.NotNil(t, data["computed_at"], "Computed at should be set")
			},
		},
		{
			name: "stats_disabled_source",
			setupDB: func(db *SourceTestDB) int64 {
				// Create a disabled source
				result, err := db.DB.Exec(`
					INSERT INTO sources (name, channel_type, feed_url, category, enabled, default_weight)
					VALUES (?, ?, ?, ?, ?, ?)
				`, "Disabled Source", "rss", "https://disabled.com/feed.xml", "center", false, 1.0)
				assert.NoError(t, err)
				sourceID, _ := result.LastInsertId()
				return sourceID
			},
			sourceID:       "1",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.True(t, response["success"].(bool), "Success should be true")
				data := response["data"].(map[string]interface{})

				// Stats should still be available for disabled sources
				assert.Equal(t, float64(1), data["source_id"], "Source ID should match")
				assert.Equal(t, float64(0), data["article_count"], "Article count should be 0 (placeholder)")
				assert.Nil(t, data["avg_score"], "Avg score should be nil")
				assert.NotNil(t, data["computed_at"], "Computed at should be set")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test database
			testDB := setupSourceTestDB(t)

			// Setup test data
			expectedID := tt.setupDB(testDB)
			t.Logf("Created source with ID: %d", expectedID)

			// Create handler
			handler := getSourceStatsHandler(testDB.DB)

			// Setup router
			router := gin.New()
			router.GET("/api/sources/:id/stats", handler)

			// Create request
			req := httptest.NewRequest("GET", "/api/sources/"+tt.sourceID+"/stats", nil)
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Verify response status
			assert.Equal(t, tt.expectedStatus, w.Code, "Unexpected status code")

			// Parse response
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err, "Failed to parse response JSON")

			// Run custom response checks
			tt.checkResponse(t, response)
		})
	}
}

func TestParseSourceID(t *testing.T) {
	tests := []struct {
		name     string
		idParam  string
		expected int
		wantErr  bool
	}{
		{
			name:     "valid ID",
			idParam:  "123",
			expected: 123,
			wantErr:  false,
		},
		{
			name:     "invalid ID - not a number",
			idParam:  "abc",
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "invalid ID - empty",
			idParam:  "",
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "invalid ID - negative",
			idParam:  "-1",
			expected: -1,
			wantErr:  false, // strconv.Atoi allows negative numbers
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := strconv.Atoi(tt.idParam)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestAdminSourceFormHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()

	// Mock template loading - in real tests we'd need proper templates
	router.LoadHTMLGlob("../../templates/**/*")

	// We can't easily test the actual handler without a database connection
	// So let's test the basic routing and parameter parsing
	router.GET("/htmx/sources/new", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"action": "new"})
	})

	// Create a test request
	req, err := http.NewRequest("GET", "/htmx/sources/new", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Check the response
	assert.Equal(t, http.StatusOK, w.Code)

	// The response should contain the action
	body := w.Body.String()
	assert.Contains(t, body, "new")
}

func TestAdminSourceFormHandlerWithID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.LoadHTMLGlob("../../templates/**/*")

	router.GET("/htmx/sources/:id/edit", func(c *gin.Context) {
		id := c.Param("id")
		c.JSON(http.StatusOK, gin.H{"action": "edit", "id": id})
	})

	// Test with ID parameter
	req, err := http.NewRequest("GET", "/htmx/sources/123/edit", nil)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()
	assert.Contains(t, body, "edit")
	assert.Contains(t, body, "123")
}

func TestValidateSourceID(t *testing.T) {
	tests := []struct {
		name    string
		idStr   string
		wantID  int
		wantErr bool
	}{
		{
			name:    "valid positive ID",
			idStr:   "42",
			wantID:  42,
			wantErr: false,
		},
		{
			name:    "zero ID",
			idStr:   "0",
			wantID:  0,
			wantErr: false,
		},
		{
			name:    "negative ID",
			idStr:   "-1",
			wantID:  -1,
			wantErr: false,
		},
		{
			name:    "invalid ID - letters",
			idStr:   "abc",
			wantID:  0,
			wantErr: true,
		},
		{
			name:    "invalid ID - empty",
			idStr:   "",
			wantID:  0,
			wantErr: true,
		},
		{
			name:    "invalid ID - mixed",
			idStr:   "123abc",
			wantID:  0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			id, err := strconv.Atoi(tt.idStr)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantID, id)
			}
		})
	}
}

func TestHTTPStatusCodes(t *testing.T) {
	// Test that we're using the correct HTTP status codes
	assert.Equal(t, 200, http.StatusOK)
	assert.Equal(t, 400, http.StatusBadRequest)
	assert.Equal(t, 404, http.StatusNotFound)
	assert.Equal(t, 500, http.StatusInternalServerError)
}

func TestGinContextMethods(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test that gin context methods work as expected
	router := gin.New()

	router.GET("/test/:id", func(c *gin.Context) {
		id := c.Param("id")
		c.JSON(http.StatusOK, gin.H{"id": id})
	})

	req, _ := http.NewRequest("GET", "/test/123", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "123")
}

func TestQueryParameterParsing(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()

	router.GET("/test", func(c *gin.Context) {
		channelType := c.Query("channel_type")
		category := c.Query("category")
		c.JSON(http.StatusOK, gin.H{
			"channel_type": channelType,
			"category":     category,
		})
	})

	req, _ := http.NewRequest("GET", "/test?channel_type=rss&category=center", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "rss")
	assert.Contains(t, w.Body.String(), "center")
}

func TestHTMLTemplateResponse(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test basic HTML response functionality
	router := gin.New()

	// Test JSON response instead of HTML template
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"title": "Test Page",
		})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "Test Page")
}

func TestErrorHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()

	router.GET("/error", func(c *gin.Context) {
		c.JSON(http.StatusInternalServerError, gin.H{
			"Error": "Test error message",
		})
	})

	req, _ := http.NewRequest("GET", "/error", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "Test error message")
}
