name: "Test Coverage Improvement Plan - Admin & Source Handlers"
description: |

## Purpose
Increase test coverage for internal/api/admin_handlers.go and internal/api/source_handlers.go from current ~20.8% to ≥80% to meet SonarCloud quality gate requirements.

## Core Principles
1. **Comprehensive Coverage**: Test all critical paths, error conditions, and edge cases
2. **Real Database Testing**: Use actual SQLite connections with test data
3. **Mock External Dependencies**: Mock RSS collectors, LLM clients appropriately
4. **Validation Loops**: Ensure tests are reliable and maintainable
5. **Follow Existing Patterns**: Use established test patterns from the codebase

---

## Goal
Achieve ≥80% test coverage for:
- `internal/api/admin_handlers.go` (895 lines, currently ~20% covered)
- `internal/api/source_handlers.go` (470 lines, currently ~20% covered)

## Why
- **SonarCloud Quality Gate**: Requires ≥80% coverage on new lines for PR approval
- **Code Reliability**: Ensure admin operations and source management work correctly
- **Regression Prevention**: Catch breaking changes in critical admin functionality
- **Maintainability**: Well-tested code is easier to refactor and extend

## What
Comprehensive test suites covering:
- All HTTP handlers with various input scenarios
- Database operations with real SQLite connections
- Error handling and edge cases
- Mock integrations with external services (RSS, LLM)
- HTMX template rendering for admin interfaces

### Success Criteria
- [ ] admin_handlers.go achieves ≥80% line coverage
- [ ] source_handlers.go achieves ≥80% line coverage
- [ ] All tests pass consistently in CI/CD pipeline
- [ ] Tests use real database connections (not mocks)
- [ ] Error paths and edge cases are thoroughly tested

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- file: internal/api/admin_handlers_basic_test.go
  why: Existing test patterns and mock structures to follow
  
- file: internal/api/source_handlers_test.go
  why: Current test approach and gaps to address
  
- file: internal/api/admin_handlers.go
  why: All functions that need comprehensive test coverage
  
- file: internal/api/source_handlers.go
  why: All CRUD operations and validation logic to test

- file: internal/db/db.go
  why: Database operations and connection patterns for tests

- file: internal/models/models.go
  why: Data structures and validation methods used in handlers
```

### Current Test Coverage Analysis
Based on coverage.out analysis:
- **admin_handlers.go**: Many functions have 0% coverage (lines 1188-1295)
- **source_handlers.go**: Basic handler structure tested but missing comprehensive scenarios
- **Missing Coverage Areas**:
  - Database error scenarios
  - Input validation edge cases
  - Async operation handling
  - HTMX template rendering
  - Complex admin operations (reanalysis, cleanup, export)

### Known Gotchas of our codebase & Library Quirks
```go
// CRITICAL: Use modernc.org/sqlite driver consistently (not mattn/go-sqlite3)
// CRITICAL: Tests need real database connections for proper coverage
// CRITICAL: Admin operations use goroutines - need proper synchronization in tests
// CRITICAL: HTMX handlers return HTML templates - need template loading in tests
// CRITICAL: Mock RSS collectors and LLM clients but test database operations with real DB
// CRITICAL: Use testify/assert and testify/mock for consistent test patterns
```

## Implementation Blueprint

### Data models and structure
```go
// Test database setup with proper isolation
type TestDB struct {
    *sqlx.DB
    cleanup func()
}

// Mock structures for external dependencies
type MockRSSCollector struct {
    mock.Mock
}

type MockLLMClient struct {
    mock.Mock
}

type MockScoreManager struct {
    mock.Mock
}
```

### List of tasks to be completed to fulfill the PRP

```yaml
Task 1: Enhance admin_handlers_basic_test.go
  - ADD comprehensive database setup/teardown
  - ADD tests for all uncovered admin functions
  - ADD error scenario testing
  - ADD async operation validation

Task 2: Expand source_handlers_test.go  
  - ADD full CRUD operation testing with real database
  - ADD input validation edge cases
  - ADD error handling scenarios
  - ADD pagination and filtering tests

Task 3: Create admin_handlers_database_test.go
  - ADD tests for database-heavy operations
  - ADD transaction testing
  - ADD cleanup and optimization operations
  - ADD export functionality testing

Task 4: Create source_handlers_integration_test.go
  - ADD end-to-end source management workflows
  - ADD HTMX template rendering tests
  - ADD complex validation scenarios
  - ADD concurrent operation testing

Task 5: Add test utilities and helpers
  - CREATE test database factory
  - CREATE common mock setups
  - CREATE test data generators
  - CREATE assertion helpers
```

### Per task pseudocode

```go
// Task 1: Enhanced admin handler tests
func TestAdminReanalyzeRecentHandler(t *testing.T) {
    // PATTERN: Setup test database with articles
    db := setupTestDB(t)
    defer db.cleanup()
    
    // PATTERN: Insert test articles for reanalysis
    insertTestArticles(db, 10, time.Now().Add(-3*24*time.Hour))
    
    // PATTERN: Mock LLM client with expectations
    mockLLM := &MockLLMClient{}
    mockLLM.On("ValidateAPIKey").Return(nil)
    mockLLM.On("ReanalyzeArticle", mock.Anything, mock.Anything, mock.Anything).Return(nil)
    
    // CRITICAL: Test async operation completion
    handler := adminReanalyzeRecentHandler(mockLLM, mockScoreManager, db.DB)
    
    // Test success case, error cases, validation failures
}

// Task 2: Source handler comprehensive tests  
func TestCreateSourceHandler_ValidationScenarios(t *testing.T) {
    tests := []struct {
        name           string
        request        models.CreateSourceRequest
        expectedStatus int
        expectedError  string
    }{
        // PATTERN: Test all validation rules
        {"valid_request", validRequest, 201, ""},
        {"empty_name", emptyNameRequest, 400, "name is required"},
        {"invalid_url", invalidURLRequest, 400, "invalid feed URL"},
        // ... comprehensive validation scenarios
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // PATTERN: Fresh database for each test
            db := setupTestDB(t)
            defer db.cleanup()
            
            // Test with real database operations
        })
    }
}
```

### Integration Points
```yaml
DATABASE:
  - setup: "Use modernc.org/sqlite with in-memory databases for tests"
  - pattern: "CREATE TABLE IF NOT EXISTS for test schema setup"
  - cleanup: "DROP TABLE and close connections after each test"

TEMPLATES:
  - setup: "Load HTML templates for HTMX handler testing"
  - pattern: "gin.LoadHTMLGlob for template rendering tests"

MOCKS:
  - rss: "Mock RSS collector for feed operations"
  - llm: "Mock LLM client for analysis operations"
  - external: "Mock external API calls and timeouts"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
go fmt ./internal/api/...                    # Format code
go vet ./internal/api/...                    # Static analysis
golangci-lint run ./internal/api/...         # Comprehensive linting

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests - Incremental Coverage Improvement
```bash
# Test individual functions with coverage tracking
go test ./internal/api -coverprofile=coverage.out -v

# Check coverage for specific files
go tool cover -func=coverage.out | grep -E "(admin_handlers|source_handlers)"

# Target: Each iteration should increase coverage by 10-15%
# Run after each batch of new tests to track progress
```

### Level 3: Integration Testing
```bash
# Run full test suite with race detection
go test ./internal/api -race -v

# Test with real database operations
go test ./internal/api -tags=integration -v

# Expected: All tests pass, no race conditions detected
```

## Detailed Implementation Tasks

### Task 1: Enhanced admin_handlers_basic_test.go (Target: +30% coverage)

**Functions to add comprehensive tests for:**
- `adminReanalyzeRecentHandler` - Test async reanalysis workflow
- `adminClearAnalysisErrorsHandler` - Test database error clearing
- `adminValidateBiasScoresHandler` - Test score validation logic
- `adminOptimizeDatabaseHandler` - Test database optimization
- `adminExportDataHandler` - Test CSV export functionality
- `adminCleanupOldArticlesHandler` - Test transaction-based cleanup
- `adminGetMetricsHandler` - Test metrics aggregation
- `adminRunHealthCheckHandler` - Test system health checks

**Test scenarios to cover:**
```go
// Database connection failures
// Invalid input parameters
// Timeout scenarios
// Concurrent operation handling
// Transaction rollback scenarios
// Large dataset handling
```

### Task 2: Expanded source_handlers_test.go (Target: +25% coverage)

**Functions needing comprehensive coverage:**
- `getSourcesHandler` - All query parameter combinations
- `createSourceHandler` - Validation and conflict scenarios
- `getSourceByIDHandler` - Error cases and edge conditions
- `updateSourceHandler` - Partial updates and validation
- `deleteSourceHandler` - Soft delete operations
- `getSourceStatsHandler` - Statistics calculation

**Critical test scenarios:**
```go
// Pagination edge cases (offset > total, limit = 0)
// Invalid query parameters
// Database constraint violations
// Concurrent source modifications
// Large result set handling
// Template rendering for HTMX endpoints
```

### Task 3: New admin_handlers_database_test.go (Target: +20% coverage)

**Focus on database-intensive operations:**
- Transaction handling in cleanup operations
- Bulk data operations (export, analysis)
- Database optimization and maintenance
- Error recovery and rollback scenarios
- Performance with large datasets

### Task 4: New source_handlers_integration_test.go (Target: +15% coverage)

**End-to-end workflow testing:**
- Complete source lifecycle (create → update → delete)
- HTMX form submission and response rendering
- Source validation with external feed checking
- Statistics computation and caching
- Concurrent source management operations

### Task 5: Test utilities and helpers

**Create reusable test infrastructure:**
```go
// testutil/database.go
func SetupTestDB(t *testing.T) *TestDB
func InsertTestSources(db *sqlx.DB, count int) []models.Source
func InsertTestArticles(db *sqlx.DB, sourceID int64, count int) []models.Article

// testutil/mocks.go
func NewMockRSSCollector() *MockRSSCollector
func NewMockLLMClient() *MockLLMClient
func SetupMockExpectations(mocks ...interface{})

// testutil/assertions.go
func AssertHTMLResponse(t *testing.T, w *httptest.ResponseRecorder, expectedElements []string)
func AssertJSONResponse(t *testing.T, w *httptest.ResponseRecorder, expected interface{})
```

## Final Validation Checklist
- [ ] admin_handlers.go coverage ≥80%: `go tool cover -func=coverage.out | grep admin_handlers`
- [ ] source_handlers.go coverage ≥80%: `go tool cover -func=coverage.out | grep source_handlers`
- [ ] All tests pass: `go test ./internal/api -v`
- [ ] No race conditions: `go test ./internal/api -race`
- [ ] Linting clean: `golangci-lint run ./internal/api`
- [ ] Integration tests pass: `go test ./internal/api -tags=integration`
- [ ] SonarCloud quality gate passes in CI/CD
- [ ] Test execution time reasonable (<30s for full suite)

---

## Anti-Patterns to Avoid
- ❌ Don't mock database operations - use real SQLite connections
- ❌ Don't skip error path testing - these are critical for admin operations
- ❌ Don't use hardcoded test data - generate dynamic test scenarios
- ❌ Don't ignore goroutine synchronization in async operation tests
- ❌ Don't test only happy paths - admin operations must handle failures gracefully
- ❌ Don't create flaky tests - ensure deterministic test execution
- ❌ Don't skip template rendering tests for HTMX endpoints
