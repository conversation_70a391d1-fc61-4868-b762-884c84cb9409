package api

import (
	"bytes"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	_ "modernc.org/sqlite"
)

func init() {
	gin.SetMode(gin.TestMode)
}

// TestMassiveCoverageBoost targets the biggest coverage gaps
func TestMassiveCoverageBoost(t *testing.T) {
	// Test various error paths in handlers
	t.Run("Handler_ErrorPaths", func(t *testing.T) {
		db, _ := sqlx.Open("sqlite", ":memory:")
		defer db.Close()

		// Test biasHandler with invalid ID
		biasHandler := biasHandler(db)
		router := gin.New()
		router.GET("/bias/:id", biasHandler)

		req := httptest.NewRequest("GET", "/bias/invalid", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, 400, w.Code)

		// Test manualScoreHandler with invalid ID
		manualHandler := manualScoreHandler(db)
		router2 := gin.New()
		router2.POST("/manual/:id", manualHandler)

		req2 := httptest.NewRequest("POST", "/manual/invalid", bytes.NewBuffer([]byte(`{"score": 0.5}`)))
		req2.Header.Set("Content-Type", "application/json")
		w2 := httptest.NewRecorder()
		router2.ServeHTTP(w2, req2)
		assert.Equal(t, 400, w2.Code)
	})

	// Test ensembleDetailsHandler
	t.Run("ensembleDetailsHandler_InvalidID", func(t *testing.T) {
		db, _ := sqlx.Open("sqlite", ":memory:")
		defer db.Close()

		handler := ensembleDetailsHandler(db)
		router := gin.New()
		router.GET("/api/articles/:id/ensemble", handler)

		req := httptest.NewRequest("GET", "/api/articles/invalid/ensemble", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 400, w.Code)
	})
}

// TestAdditionalHandlerCoverage covers more handler scenarios
func TestAdditionalHandlerCoverage(t *testing.T) {
	// Test additional error paths
	t.Run("AdditionalErrorPaths", func(t *testing.T) {
		db, _ := sqlx.Open("sqlite", ":memory:")
		defer db.Close()

		// Test getArticleByIDHandler with invalid ID
		handler := getArticleByIDHandler(db)
		router := gin.New()
		router.GET("/article/:id", handler)

		req := httptest.NewRequest("GET", "/article/invalid", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, 400, w.Code)

		// Test createArticleHandler with invalid JSON
		createHandler := createArticleHandler(db)
		router2 := gin.New()
		router2.POST("/articles", createHandler)

		req2 := httptest.NewRequest("POST", "/articles", bytes.NewBuffer([]byte("invalid json")))
		req2.Header.Set("Content-Type", "application/json")
		w2 := httptest.NewRecorder()
		router2.ServeHTTP(w2, req2)
		assert.Equal(t, 400, w2.Code)
	})

	// Test various utility functions and edge cases
	t.Run("UtilityFunctions_EdgeCases", func(t *testing.T) {

		// Test different response scenarios
		router := gin.New()

		// Test RespondError with different error types
		router.GET("/error1", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrValidation, "validation failed"))
		})

		router.GET("/error2", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrNotFound, "not found"))
		})

		router.GET("/error3", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrConflict, "conflict"))
		})

		router.GET("/error4", func(c *gin.Context) {
			RespondError(c, NewAppError(ErrInternal, "internal error"))
		})

		// Test all error types
		testCases := []struct {
			path           string
			expectedStatus int
		}{
			{"/error1", 400},
			{"/error2", 404},
			{"/error3", 409},
			{"/error4", 500},
		}

		for _, tc := range testCases {
			req := httptest.NewRequest("GET", tc.path, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			assert.Equal(t, tc.expectedStatus, w.Code)
		}
	})

	// Test database operations and edge cases
	t.Run("DatabaseOperations_EdgeCases", func(t *testing.T) {
		db, _ := sqlx.Open("sqlite", ":memory:")
		defer db.Close()

		// Create minimal schema for testing
		schema := `
		CREATE TABLE articles (
			id INTEGER PRIMARY KEY,
			title TEXT,
			content TEXT,
			source TEXT,
			url TEXT UNIQUE,
			pub_date TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			status TEXT DEFAULT 'pending'
		);`
		db.Exec(schema)

		// Test createArticleHandler with various edge cases
		handler := createArticleHandler(db)
		router := gin.New()
		router.POST("/articles", handler)

		// Test missing required fields
		req := httptest.NewRequest("POST", "/articles", bytes.NewBuffer([]byte(`{}`)))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, 400, w.Code)

		// Test invalid JSON
		req2 := httptest.NewRequest("POST", "/articles", bytes.NewBuffer([]byte(`invalid json`)))
		req2.Header.Set("Content-Type", "application/json")
		w2 := httptest.NewRecorder()
		router.ServeHTTP(w2, req2)
		assert.Equal(t, 400, w2.Code)

		// Test getArticlesHandler with empty database
		getHandler := getArticlesHandler(db)
		router2 := gin.New()
		router2.GET("/articles", getHandler)

		req3 := httptest.NewRequest("GET", "/articles", nil)
		w3 := httptest.NewRecorder()
		router2.ServeHTTP(w3, req3)
		// Should return 200 with empty results
		assert.Equal(t, 200, w3.Code)
	})
}

// TestRemainingCoverage targets any remaining uncovered code paths
func TestRemainingCoverage(t *testing.T) {
	// Test SafeHandler with panic recovery
	t.Run("SafeHandler_PanicRecovery", func(t *testing.T) {
		handler := SafeHandler(func(c *gin.Context) {
			panic("test panic")
		})

		router := gin.New()
		router.GET("/panic", handler)

		req := httptest.NewRequest("GET", "/panic", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return 500 due to panic recovery
		assert.Equal(t, 500, w.Code)
	})

	// Test various helper functions
	t.Run("HelperFunctions_Coverage", func(t *testing.T) {
		// Test safeLogf with different scenarios
		safeLogf("Test message")
		safeLogf("Test message with param: %s", "value")
		safeLogf("Test message with multiple params: %s %d", "value", 123)

		// Test error creation and handling
		err1 := NewAppError(ErrValidation, "test validation error")
		assert.NotNil(t, err1)
		assert.Equal(t, ErrValidation, err1.Code)

		err2 := NewAppError(ErrNotFound, "test not found error")
		assert.NotNil(t, err2)
		assert.Equal(t, ErrNotFound, err2.Code)
	})
}
